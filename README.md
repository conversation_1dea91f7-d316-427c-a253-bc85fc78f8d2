# ES异常值检测系统

这是一个用于检测Elasticsearch中指定字段异常值的Python工具，能够根据配置的门限值范围自动检测异常并生成新的索引。

## 功能特性

- 支持多个ES索引的同时监控
- 灵活的时间范围配置（ALL、LastDay、具体日期）
- 可配置的字段门限值范围
- 自动生成异常记录并保存到新索引
- 详细的日志记录和统计信息
- 使用scroll API处理大数据量

## 项目结构

```
es_exception_value_to_es/
├── config.json              # 配置文件
├── main.py                  # 主程序入口
├── es_client.py             # ES客户端封装
├── exception_detector.py    # 异常值检测逻辑
├── data_processor.py        # 数据处理逻辑
├── requirements.txt         # 依赖包
└── README.md               # 说明文档
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置说明

编辑 `config.json` 文件：

### 1. ES连接配置
```json
"es_config": {
  "host": "http://***************:9200"
}
```

### 2. 时间范围配置
```json
"time_range": {
  "start_date": "ALL",     // 可选值: "ALL", "LastDay", "YYYY-MM-DD"
  "end_date": "ALL"
}
```

时间范围选项说明：
- `"ALL"`: 扫描索引中的所有记录
- `"LastDay"`: 扫描当前日期前一天的记录
- `"YYYY-MM-DD"`: 具体日期，如 "2024-05-01"

### 3. 监控规则配置
```json
"monitoring_rules": {
  "psis-collector-cpu-index": {
    "cpu_usage": {"min": 0, "max": 0.5},
    "mem_usage": {"min": 0, "max": 50},
    "process_max_cpu_use": {"min": 0, "max": 0.8}
  },
  "psis-collector-harddisk-index": {
    "mmcblk0p1_usage": {"min": 0, "max": 0.8},
    // ... 更多字段
  }
}
```

### 4. 输出索引配置
```json
"output_index": "exception-monitoring-results"
```

## 使用方法

1. 修改 `config.json` 配置文件
2. 运行主程序：

```bash
python main.py
```

## 输出结果

程序会在ES中创建一个新的索引（默认为 `exception-monitoring-results`），包含以下字段：

- `Trydate`: 原记录的日期
- `IP`: 原记录的IP地址
- `Owner`: 原记录的所有者
- `Version`: 原记录的版本
- `key_name`: 异常字段名称
- `value`: 异常字段值
- `threshold_range`: 门限值范围（如 "0~0.5"）
- `index_name`: 来源索引名称
- `detection_time`: 检测时间

## 日志文件

程序运行时会生成 `es_exception_monitor.log` 日志文件，记录详细的执行过程和错误信息。

## 注意事项

1. 确保ES服务可访问
2. 确保配置的索引存在
3. 大数据量处理时请注意ES集群性能
4. 建议在测试环境先验证配置正确性

## 示例配置

### 检测昨天的数据
```json
{
  "time_range": {
    "start_date": "LastDay",
    "end_date": "LastDay"
  }
}
```

### 检测指定日期范围
```json
{
  "time_range": {
    "start_date": "2024-05-01",
    "end_date": "2024-05-31"
  }
}
```

### 检测所有数据
```json
{
  "time_range": {
    "start_date": "ALL",
    "end_date": "ALL"
  }
}
```
