"""
Elasticsearch客户端封装模块
"""
import json
from typing import List, Dict, Any, Generator
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk, scan
import logging

logger = logging.getLogger(__name__)


class ESClient:
    """Elasticsearch客户端封装类"""
    
    def __init__(self, host: str):
        """
        初始化ES客户端
        
        Args:
            host: ES主机地址
        """
        self.host = host
        self.client = None
        self._connect()
    
    def _connect(self):
        """连接到Elasticsearch"""
        try:
            self.client = Elasticsearch([self.host])
            # 测试连接
            if self.client.ping():
                logger.info(f"成功连接到Elasticsearch: {self.host}")
            else:
                raise ConnectionError("无法连接到Elasticsearch")
        except Exception as e:
            logger.error(f"连接Elasticsearch失败: {e}")
            raise
    
    def search_with_scroll(self, index: str, query: Dict[str, Any], 
                          scroll_size: int = 1000) -> Generator[Dict[str, Any], None, None]:
        """
        使用scroll API搜索数据
        
        Args:
            index: 索引名称
            query: 查询条件
            scroll_size: 每次scroll返回的文档数量
            
        Yields:
            文档数据
        """
        try:
            # 使用scan helper进行scroll查询
            for doc in scan(
                self.client,
                query=query,
                index=index,
                size=scroll_size,
                scroll='5m'
            ):
                yield doc['_source']
        except Exception as e:
            logger.error(f"搜索索引 {index} 失败: {e}")
            raise
    
    def bulk_index(self, index: str, documents: List[Dict[str, Any]]) -> bool:
        """
        批量索引文档
        
        Args:
            index: 目标索引名称
            documents: 要索引的文档列表
            
        Returns:
            是否成功
        """
        if not documents:
            logger.warning("没有文档需要索引")
            return True
            
        try:
            # 准备批量操作的数据
            actions = []
            for doc in documents:
                action = {
                    "_index": index,
                    "_source": doc
                }
                actions.append(action)
            
            # 执行批量操作
            success_count, failed_items = bulk(self.client, actions, raise_on_error=False)
            logger.info("批量索引完成: 成功 %d 条", success_count)

            if failed_items:
                logger.error("批量索引失败的文档数量: %d", len(failed_items))
                for item in failed_items[:5]:  # 只显示前5个错误
                    logger.error("失败详情: %s", item)
                return False

            return True
            
        except Exception as e:
            logger.error(f"批量索引失败: {e}")
            raise
    
    def create_index_if_not_exists(self, index: str, mapping: Dict[str, Any] = None):
        """
        如果索引不存在则创建
        
        Args:
            index: 索引名称
            mapping: 索引映射配置
        """
        try:
            if not self.client.indices.exists(index=index):
                body = {}
                if mapping:
                    body["mappings"] = mapping
                
                self.client.indices.create(index=index, body=body)
                logger.info(f"创建索引: {index}")
            else:
                logger.info(f"索引已存在: {index}")
        except Exception as e:
            logger.error(f"创建索引失败: {e}")
            raise
    
    def get_index_count(self, index: str, query: Dict[str, Any] = None) -> int:
        """
        获取索引中文档数量
        
        Args:
            index: 索引名称
            query: 查询条件
            
        Returns:
            文档数量
        """
        try:
            body = {"query": query} if query else {}
            result = self.client.count(index=index, body=body)
            return result['count']
        except Exception as e:
            logger.error(f"获取索引 {index} 文档数量失败: {e}")
            return 0
