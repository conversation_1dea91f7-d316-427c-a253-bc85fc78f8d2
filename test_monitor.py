"""
测试ES异常值检测功能
"""
import sys
import logging
from main import ESExceptionMonitor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_connection():
    """测试ES连接"""
    try:
        monitor = ESExceptionMonitor("test_config.json")
        monitor._initialize_components()
        
        # 测试连接
        if monitor.es_client.client.ping():
            print("✓ ES连接测试成功")
            return True
        else:
            print("✗ ES连接测试失败")
            return False
    except Exception as e:
        print(f"✗ ES连接测试失败: {e}")
        return False


def test_index_exists():
    """测试索引是否存在"""
    try:
        monitor = ESExceptionMonitor("test_config.json")
        monitor._initialize_components()
        
        monitoring_rules = monitor.config["monitoring_rules"]
        
        for index_name in monitoring_rules.keys():
            exists = monitor.es_client.client.indices.exists(index=index_name)
            if exists:
                count = monitor.es_client.get_index_count(index_name)
                print(f"✓ 索引 {index_name} 存在，文档数量: {count}")
            else:
                print(f"✗ 索引 {index_name} 不存在")
        
        return True
    except Exception as e:
        print(f"✗ 索引检查失败: {e}")
        return False


def test_data_processing():
    """测试数据处理功能"""
    try:
        from data_processor import DataProcessor
        
        # 测试时间范围解析
        test_cases = [
            ("ALL", "ALL"),
            ("LastDay", "LastDay"),
            ("2024-05-01", "2024-05-31")
        ]
        
        for start, end in test_cases:
            try:
                parsed_start, parsed_end = DataProcessor.parse_time_range(start, end)
                print(f"✓ 时间范围解析成功: {start}, {end} -> {parsed_start}, {parsed_end}")
            except Exception as e:
                print(f"✗ 时间范围解析失败: {start}, {end} -> {e}")
        
        # 测试查询构建
        query = DataProcessor.build_es_query("LastDay", "LastDay")
        print(f"✓ ES查询构建成功: {query}")
        
        return True
    except Exception as e:
        print(f"✗ 数据处理测试失败: {e}")
        return False


def test_exception_detection():
    """测试异常检测功能"""
    try:
        from exception_detector import ExceptionDetector
        
        # 模拟监控规则
        rules = {
            "test-index": {
                "cpu_usage": {"min": 0, "max": 0.5},
                "mem_usage": {"min": 0, "max": 50}
            }
        }
        
        detector = ExceptionDetector(rules)
        
        # 模拟文档数据
        test_documents = [
            {
                "TryDate": "2024-05-01",
                "IP": "*************",
                "Owner": "system",
                "Version": "1.0",
                "cpu_usage": 0.8,  # 超出门限
                "mem_usage": 30    # 正常
            },
            {
                "TryDate": "2024-05-01",
                "IP": "*************",
                "Owner": "system",
                "Version": "1.0",
                "cpu_usage": 0.3,  # 正常
                "mem_usage": 60    # 超出门限
            }
        ]
        
        total_exceptions = 0
        for doc in test_documents:
            exceptions = detector.detect_exceptions_in_document(doc, "test-index")
            total_exceptions += len(exceptions)
            print(f"✓ 文档检测完成，发现 {len(exceptions)} 个异常")
        
        print(f"✓ 异常检测测试完成，总共发现 {total_exceptions} 个异常")
        
        # 获取统计信息
        stats = detector.get_statistics()
        print(f"✓ 统计信息: {stats}")
        
        return True
    except Exception as e:
        print(f"✗ 异常检测测试失败: {e}")
        return False


def run_full_test():
    """运行完整测试"""
    try:
        print("开始运行ES异常值检测测试...")
        monitor = ESExceptionMonitor("test_config.json")
        
        # 运行检测（使用较小的数据集）
        statistics = monitor.run_detection()
        
        print("\n测试完成!")
        print(f"发现异常数量: {statistics['total_exceptions']}")
        print(f"按索引统计: {statistics['exceptions_by_index']}")
        print(f"按字段统计: {statistics['exceptions_by_field']}")
        
        return True
    except Exception as e:
        print(f"✗ 完整测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("ES异常值检测系统测试")
    print("=" * 50)
    
    tests = [
        ("ES连接测试", test_connection),
        ("索引存在性检查", test_index_exists),
        ("数据处理功能测试", test_data_processing),
        ("异常检测功能测试", test_exception_detection),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行测试: {test_name}")
        print("-" * 30)
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n所有基础测试通过，是否运行完整测试？(y/n)")
        choice = input().lower()
        if choice == 'y':
            print("\n运行完整测试...")
            run_full_test()


if __name__ == "__main__":
    main()
