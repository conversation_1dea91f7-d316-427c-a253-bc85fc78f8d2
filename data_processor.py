"""
数据处理模块
"""
from datetime import datetime, timedelta
from typing import Dict, Any, Tuple
import logging

logger = logging.getLogger(__name__)


class DataProcessor:
    """数据处理类"""
    
    @staticmethod
    def parse_time_range(start_date: str, end_date: str) -> Tuple[str, str]:
        """
        解析时间范围
        
        Args:
            start_date: 开始日期 (ALL/LastDay/YYYY-MM-DD)
            end_date: 结束日期 (ALL/LastDay/YYYY-MM-DD)
            
        Returns:
            解析后的开始和结束日期元组
        """
        try:
            if start_date == "ALL" and end_date == "ALL":
                return None, None
            
            if start_date == "LastDay" and end_date == "LastDay":
                yesterday = datetime.now() - timedelta(days=1)
                date_str = yesterday.strftime("%Y-%m-%d")
                return date_str, date_str
            
            # 验证日期格式
            if start_date != "ALL":
                datetime.strptime(start_date, "%Y-%m-%d")
            if end_date != "ALL":
                datetime.strptime(end_date, "%Y-%m-%d")
            
            return start_date, end_date
            
        except ValueError as e:
            logger.error(f"日期格式错误: {e}")
            raise ValueError(f"日期格式必须为 YYYY-MM-DD: {e}")
    
    @staticmethod
    def build_time_query(start_date: str, end_date: str, date_field: str = "TryDate") -> Dict[str, Any]:
        """
        构建时间范围查询条件
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            date_field: 日期字段名
            
        Returns:
            ES查询条件
        """
        parsed_start, parsed_end = DataProcessor.parse_time_range(start_date, end_date)
        
        if parsed_start is None and parsed_end is None:
            # 查询所有数据
            return {"match_all": {}}
        
        # 构建日期范围查询
        range_query = {
            "range": {
                date_field: {}
            }
        }
        
        if parsed_start:
            range_query["range"][date_field]["gte"] = parsed_start
        if parsed_end:
            range_query["range"][date_field]["lte"] = parsed_end
        
        return range_query
    
    @staticmethod
    def build_es_query(start_date: str, end_date: str, date_field: str = "TryDate") -> Dict[str, Any]:
        """
        构建完整的ES查询
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            date_field: 日期字段名
            
        Returns:
            完整的ES查询体
        """
        time_query = DataProcessor.build_time_query(start_date, end_date, date_field)
        
        return {
            "query": time_query,
            "sort": [
                {date_field: {"order": "desc"}}
            ]
        }
    
    @staticmethod
    def extract_base_fields(document: Dict[str, Any]) -> Dict[str, Any]:
        """
        从文档中提取基础字段
        
        Args:
            document: ES文档
            
        Returns:
            包含基础字段的字典
        """
        base_fields = {}
        
        # 提取必要的基础字段
        field_mappings = {
            "Trydate": ["TryDate", "trydate", "date", "@timestamp"],
            "IP": ["IP", "ip", "host", "hostname"],
            "Owner": ["Owner", "owner", "user", "username"],
            "Version": ["Version", "version", "ver"]
        }
        
        for target_field, possible_fields in field_mappings.items():
            value = None
            for field in possible_fields:
                if field in document:
                    value = document[field]
                    break
            
            # 如果没有找到对应字段，设置默认值
            if value is None:
                if target_field == "Trydate":
                    value = datetime.now().strftime("%Y-%m-%d")
                else:
                    value = "unknown"
            
            base_fields[target_field] = value
        
        return base_fields
    
    @staticmethod
    def format_threshold_range(min_val: float, max_val: float) -> str:
        """
        格式化门限值范围
        
        Args:
            min_val: 最小值
            max_val: 最大值
            
        Returns:
            格式化的门限值范围字符串
        """
        return f"{min_val}~{max_val}"
    
    @staticmethod
    def is_value_out_of_range(value: Any, min_val: float, max_val: float) -> bool:
        """
        检查值是否超出范围
        
        Args:
            value: 要检查的值
            min_val: 最小值
            max_val: 最大值
            
        Returns:
            是否超出范围
        """
        try:
            # 尝试转换为数值
            numeric_value = float(value)
            return numeric_value < min_val or numeric_value > max_val
        except (ValueError, TypeError):
            logger.warning(f"无法将值 {value} 转换为数值")
            return False
