# ES异常值检测系统使用指南

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置系统
编辑 `config.json` 文件，设置您的ES连接信息和监控规则。

### 3. 运行系统
```bash
# 方式1: 使用快速启动脚本
python run.py

# 方式2: 直接运行主程序
python main.py

# 方式3: 运行测试
python test_monitor.py

# 方式4: 运行示例
python example_usage.py
```

## 配置详解

### ES连接配置
```json
"es_config": {
  "host": "http://***************:9200"
}
```

### 时间范围配置
```json
"time_range": {
  "start_date": "LastDay",  // 选项: "ALL", "LastDay", "YYYY-MM-DD"
  "end_date": "LastDay"
}
```

**时间范围选项说明：**
- `"ALL"`: 扫描所有记录
- `"LastDay"`: 扫描昨天的记录
- `"YYYY-MM-DD"`: 具体日期，如 "2024-05-01"

### 监控规则配置
```json
"monitoring_rules": {
  "索引名称": {
    "字段名": {"min": 最小值, "max": 最大值}
  }
}
```

**示例：**
```json
"psis-collector-cpu-index": {
  "cpu_usage": {"min": 0, "max": 0.5},
  "mem_usage": {"min": 0, "max": 50}
}
```

## 输出结果

系统会在ES中创建新索引，包含以下字段：

| 字段名 | 说明 |
|--------|------|
| Trydate | 原记录日期 |
| IP | 原记录IP地址 |
| Owner | 原记录所有者 |
| Version | 原记录版本 |
| key_name | 异常字段名 |
| value | 异常字段值 |
| threshold_range | 门限值范围 |
| index_name | 来源索引 |
| detection_time | 检测时间 |

## 常见使用场景

### 1. 监控昨天的数据
```json
{
  "time_range": {
    "start_date": "LastDay",
    "end_date": "LastDay"
  }
}
```

### 2. 监控指定时间段
```json
{
  "time_range": {
    "start_date": "2024-05-01",
    "end_date": "2024-05-31"
  }
}
```

### 3. 监控所有历史数据
```json
{
  "time_range": {
    "start_date": "ALL",
    "end_date": "ALL"
  }
}
```

## 故障排除

### 1. 连接ES失败
- 检查ES服务是否运行
- 验证host地址是否正确
- 确认网络连接正常

### 2. 索引不存在
- 确认索引名称拼写正确
- 检查索引是否已创建
- 验证时间范围内是否有数据

### 3. 没有发现异常
- 检查门限值设置是否合理
- 确认字段名称是否正确
- 验证数据格式是否符合预期

## 性能优化建议

1. **大数据量处理**：系统使用scroll API分批处理，适合大数据量
2. **时间范围限制**：建议设置合理的时间范围，避免处理过多数据
3. **门限值调整**：根据实际业务需求调整门限值
4. **批量大小**：可在代码中调整scroll_size参数

## 日志文件

系统运行时会生成 `es_exception_monitor.log` 日志文件，包含：
- 详细的执行过程
- 错误信息和警告
- 统计信息

## 扩展功能

系统设计为模块化架构，可以轻松扩展：
- 添加新的数据源
- 自定义异常检测规则
- 集成告警系统
- 添加可视化界面
