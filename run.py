"""
快速启动脚本
"""
import sys
import os
import subprocess


def install_dependencies():
    """安装依赖包"""
    print("正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 依赖包安装失败: {e}")
        return False


def check_config():
    """检查配置文件"""
    if not os.path.exists("config.json"):
        print("✗ 配置文件 config.json 不存在")
        return False
    
    print("✓ 配置文件存在")
    return True


def run_tests():
    """运行测试"""
    print("运行系统测试...")
    try:
        subprocess.check_call([sys.executable, "test_monitor.py"])
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 测试失败: {e}")
        return False


def run_monitor():
    """运行监控程序"""
    print("启动ES异常值监控...")
    try:
        subprocess.check_call([sys.executable, "main.py"])
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 监控程序运行失败: {e}")
        return False


def main():
    """主函数"""
    print("ES异常值检测系统启动器")
    print("=" * 40)
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("✗ 需要Python 3.6或更高版本")
        sys.exit(1)
    
    print(f"✓ Python版本: {sys.version}")
    
    # 安装依赖
    if not install_dependencies():
        sys.exit(1)
    
    # 检查配置
    if not check_config():
        print("请先配置 config.json 文件")
        sys.exit(1)
    
    # 询问用户操作
    print("\n请选择操作:")
    print("1. 运行测试")
    print("2. 直接运行监控")
    print("3. 先测试后监控")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        run_tests()
    elif choice == "2":
        run_monitor()
    elif choice == "3":
        if run_tests():
            print("\n测试通过，开始运行监控...")
            run_monitor()
    else:
        print("无效选择")
        sys.exit(1)


if __name__ == "__main__":
    main()
