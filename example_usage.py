"""
ES异常值检测使用示例
"""
import json
from main import ESExceptionMonitor


def create_sample_config():
    """创建示例配置文件"""
    config = {
        "es_config": {
            "host": "http://***************:9200"
        },
        "time_range": {
            "start_date": "LastDay",
            "end_date": "LastDay"
        },
        "monitoring_rules": {
            "psis-collector-cpu-index": {
                "cpu_usage": {"min": 0, "max": 0.5},
                "mem_usage": {"min": 0, "max": 50},
                "process_max_cpu_use": {"min": 0, "max": 0.8}
            },
            "psis-collector-harddisk-index": {
                "mmcblk0p1_usage": {"min": 0, "max": 0.8},
                "mmcblk0p2_usage": {"min": 0, "max": 0.8},
                "mmcblk0p3_Usage": {"min": 0, "max": 0.8},
                "mmcblk0p4_Usage": {"min": 0, "max": 0.8},
                "tmpfs_sysfscgroupUsage": {"min": 0, "max": 0.8},
                "devtmpfs_devUsage": {"min": 0, "max": 0.8},
                "tmpfs_runUsage": {"min": 0, "max": 0.8},
                "tmpfs_tmpUsage": {"min": 0, "max": 0.8},
                "tmpfs_runlockUsage": {"min": 0, "max": 0.8},
                "rootUsage": {"min": 0, "max": 0.8}
            }
        },
        "output_index": "example-exception-monitoring-results"
    }
    
    with open("example_config.json", "w", encoding="utf-8") as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print("示例配置文件已创建: example_config.json")


def run_example():
    """运行示例"""
    print("ES异常值检测示例")
    print("=" * 40)
    
    # 创建示例配置
    create_sample_config()
    
    try:
        # 使用示例配置运行监控
        monitor = ESExceptionMonitor("example_config.json")
        statistics = monitor.run_detection()
        
        print("\n检测结果:")
        print(f"总异常数量: {statistics['total_exceptions']}")
        print(f"按索引统计: {statistics['exceptions_by_index']}")
        print(f"按字段统计: {statistics['exceptions_by_field']}")
        
    except Exception as e:
        print(f"运行示例失败: {e}")


if __name__ == "__main__":
    run_example()
