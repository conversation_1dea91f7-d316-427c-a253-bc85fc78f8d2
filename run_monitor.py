"""
ES异常值检测运行脚本 - 支持不同配置
"""
import subprocess
import sys
import os


def run_with_config(config_type):
    """使用指定配置运行监控"""
    print(f"开始运行ES异常值检测 - 配置类型: {config_type}")
    print("=" * 60)
    
    try:
        if config_type == "all":
            print("检测范围: 所有历史数据")
            result = subprocess.run([sys.executable, "main.py", "--preset", "all"], 
                                  capture_output=False, text=True)
        elif config_type == "lastday":
            print("检测范围: 昨天的数据")
            result = subprocess.run([sys.executable, "main.py", "--preset", "lastday"], 
                                  capture_output=False, text=True)
        elif config_type == "lastmonth":
            print("检测范围: 上个月的数据")
            result = subprocess.run([sys.executable, "main.py", "--preset", "lastmonth"], 
                                  capture_output=False, text=True)
        else:
            print(f"检测范围: 自定义配置文件 {config_type}")
            result = subprocess.run([sys.executable, "main.py", "--config", config_type], 
                                  capture_output=False, text=True)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"运行失败: {e}")
        return False


def check_dependencies():
    """检查依赖"""
    print("检查依赖包...")
    try:
        import elasticsearch
        import dateutil
        print("✓ 依赖包检查通过")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖包: {e}")
        print("请运行: pip install -r requirements.txt")
        return False


def check_config_files():
    """检查配置文件"""
    config_files = [
        "config_all.json",
        "config_lastday.json", 
        "config_lastmonth.json"
    ]
    
    print("检查配置文件...")
    missing_files = []
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✓ {config_file}")
        else:
            print(f"✗ {config_file}")
            missing_files.append(config_file)
    
    if missing_files:
        print(f"缺少配置文件: {missing_files}")
        return False
    
    return True


def main():
    """主函数"""
    print("ES异常值检测系统 - 多配置运行器")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 检查配置文件
    if not check_config_files():
        print("请确保所有配置文件存在")
        sys.exit(1)
    
    print("\n请选择运行模式:")
    print("1. 检测所有历史数据 (config_all.json)")
    print("2. 检测昨天的数据 (config_lastday.json)")
    print("3. 检测上个月的数据 (config_lastmonth.json)")
    print("4. 使用自定义配置文件")
    print("5. 运行所有配置 (依次执行1-3)")
    
    choice = input("\n请输入选择 (1-5): ").strip()
    
    if choice == "1":
        success = run_with_config("all")
    elif choice == "2":
        success = run_with_config("lastday")
    elif choice == "3":
        success = run_with_config("lastmonth")
    elif choice == "4":
        config_file = input("请输入配置文件路径: ").strip()
        if not os.path.exists(config_file):
            print(f"配置文件不存在: {config_file}")
            sys.exit(1)
        success = run_with_config(config_file)
    elif choice == "5":
        print("依次运行所有配置...")
        configs = ["all", "lastday", "lastmonth"]
        success = True
        for config in configs:
            print(f"\n{'='*40}")
            print(f"运行配置: {config}")
            print(f"{'='*40}")
            if not run_with_config(config):
                success = False
                print(f"配置 {config} 运行失败")
                break
            print(f"配置 {config} 运行完成")
    else:
        print("无效选择")
        sys.exit(1)
    
    if success:
        print("\n" + "="*60)
        print("所有任务执行完成!")
        print("="*60)
    else:
        print("\n" + "="*60)
        print("任务执行失败!")
        print("="*60)
        sys.exit(1)


if __name__ == "__main__":
    main()
