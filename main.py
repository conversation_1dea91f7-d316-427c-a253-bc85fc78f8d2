"""
ES异常值检测主程序
"""
import json
import logging
import sys
from typing import Dict, Any
from es_client import ESClient
from exception_detector import ExceptionDetector
from data_processor import DataProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('es_exception_monitor.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


class ESExceptionMonitor:
    """ES异常值监控主类"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        初始化监控器
        
        Args:
            config_file: 配置文件路径
        """
        self.config = self._load_config(config_file)
        self.es_client = None
        self.detector = None
        
    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """
        加载配置文件
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            配置字典
        """
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            logger.info(f"成功加载配置文件: {config_file}")
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise
    
    def _initialize_components(self):
        """初始化各个组件"""
        try:
            # 初始化ES客户端
            es_host = self.config["es_config"]["host"]
            self.es_client = ESClient(es_host)
            
            # 初始化异常检测器
            monitoring_rules = self.config["monitoring_rules"]
            self.detector = ExceptionDetector(monitoring_rules)
            
            logger.info("组件初始化完成")
            
        except Exception as e:
            logger.error(f"组件初始化失败: {e}")
            raise
    
    def _create_output_index_mapping(self) -> Dict[str, Any]:
        """
        创建输出索引的映射配置
        
        Returns:
            映射配置
        """
        return {
            "properties": {
                "Trydate": {
                    "type": "date",
                    "format": "yyyy-MM-dd||yyyy-MM-dd HH:mm:ss||epoch_millis"
                },
                "IP": {
                    "type": "keyword"
                },
                "Owner": {
                    "type": "keyword"
                },
                "Version": {
                    "type": "keyword"
                },
                "key_name": {
                    "type": "keyword"
                },
                "value": {
                    "type": "double"
                },
                "threshold_range": {
                    "type": "keyword"
                },
                "index_name": {
                    "type": "keyword"
                },
                "detection_time": {
                    "type": "date",
                    "format": "yyyy-MM-dd||yyyy-MM-dd HH:mm:ss||epoch_millis"
                }
            }
        }
    
    def run_detection(self):
        """运行异常检测"""
        try:
            logger.info("开始ES异常值检测")
            
            # 初始化组件
            self._initialize_components()
            
            # 获取时间范围配置
            time_range = self.config["time_range"]
            start_date = time_range["start_date"]
            end_date = time_range["end_date"]
            
            logger.info(f"检测时间范围: {start_date} 到 {end_date}")
            
            # 构建查询条件
            query = DataProcessor.build_es_query(start_date, end_date)
            
            # 处理每个索引
            total_processed = 0
            monitoring_rules = self.config["monitoring_rules"]
            
            for index_name in monitoring_rules.keys():
                logger.info(f"开始处理索引: {index_name}")
                
                try:
                    # 检查索引是否存在
                    if not self.es_client.client.indices.exists(index=index_name):
                        logger.warning(f"索引 {index_name} 不存在，跳过")
                        continue
                    
                    # 获取索引文档数量
                    doc_count = self.es_client.get_index_count(index_name, query.get("query"))
                    logger.info(f"索引 {index_name} 匹配的文档数量: {doc_count}")
                    
                    if doc_count == 0:
                        logger.info(f"索引 {index_name} 没有匹配的文档，跳过")
                        continue
                    
                    # 处理索引数据
                    processed = self.detector.process_index_data(self.es_client, index_name, query)
                    total_processed += processed
                    
                except Exception as e:
                    logger.error(f"处理索引 {index_name} 时发生错误: {e}")
                    continue
            
            # 获取检测结果
            exception_records = self.detector.get_exception_records()
            statistics = self.detector.get_statistics()
            
            logger.info("检测完成 - 总处理文档数: %d, 发现异常数: %d",
                        total_processed, statistics['total_exceptions'])
            logger.info("异常统计: %s", statistics)

            # 保存结果到新索引
            if exception_records:
                self._save_results_to_index(exception_records)
            else:
                logger.info("没有发现异常，不创建输出索引")

            return statistics

        except Exception as e:
            logger.error("异常检测过程中发生错误: %s", e)
            raise
    
    def _save_results_to_index(self, exception_records: list):
        """
        保存结果到ES索引
        
        Args:
            exception_records: 异常记录列表
        """
        try:
            output_index = self.config["output_index"]
            logger.info(f"开始保存 {len(exception_records)} 条异常记录到索引: {output_index}")
            
            # 创建输出索引
            mapping = self._create_output_index_mapping()
            self.es_client.create_index_if_not_exists(output_index, mapping)
            
            # 批量保存数据
            success = self.es_client.bulk_index(output_index, exception_records)
            
            if success:
                logger.info(f"成功保存异常记录到索引: {output_index}")
            else:
                logger.error("保存异常记录失败")
                
        except Exception as e:
            logger.error(f"保存结果到索引时发生错误: {e}")
            raise


def main():
    """主函数"""
    try:
        # 创建监控器实例
        monitor = ESExceptionMonitor()
        
        # 运行检测
        statistics = monitor.run_detection()
        
        print("\n" + "="*50)
        print("ES异常值检测完成!")
        print("="*50)
        print(f"总异常数量: {statistics['total_exceptions']}")
        print("\n按索引统计:")
        for index, count in statistics['exceptions_by_index'].items():
            print(f"  {index}: {count} 个异常")
        print("\n按字段统计:")
        for field, count in statistics['exceptions_by_field'].items():
            print(f"  {field}: {count} 个异常")
        print("="*50)
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
