"""
异常检测模块
"""
from typing import List, Dict, Any
import logging
from data_processor import DataProcessor

logger = logging.getLogger(__name__)


class ExceptionDetector:
    """异常值检测器"""
    
    def __init__(self, monitoring_rules: Dict[str, Dict[str, Dict[str, float]]]):
        """
        初始化异常检测器
        
        Args:
            monitoring_rules: 监控规则配置
        """
        self.monitoring_rules = monitoring_rules
        self.exception_records = []
    
    def detect_exceptions_in_document(self, document: Dict[str, Any], index_name: str) -> List[Dict[str, Any]]:
        """
        检测单个文档中的异常值
        
        Args:
            document: ES文档
            index_name: 索引名称
            
        Returns:
            异常记录列表
        """
        exceptions = []
        
        # 获取该索引的监控规则
        if index_name not in self.monitoring_rules:
            logger.warning(f"索引 {index_name} 没有配置监控规则")
            return exceptions
        
        rules = self.monitoring_rules[index_name]
        
        # 提取基础字段
        base_fields = DataProcessor.extract_base_fields(document)
        
        # 检查每个监控字段
        for field_name, threshold in rules.items():
            if field_name in document:
                field_value = document[field_name]
                min_val = threshold["min"]
                max_val = threshold["max"]
                
                # 检查是否超出范围
                if DataProcessor.is_value_out_of_range(field_value, min_val, max_val):
                    exception_record = {
                        "Trydate": base_fields["Trydate"],
                        "IP": base_fields["IP"],
                        "Owner": base_fields["Owner"],
                        "Version": base_fields["Version"],
                        "key_name": field_name,
                        "value": field_value,
                        "threshold_range": DataProcessor.format_threshold_range(min_val, max_val),
                        "index_name": index_name,  # 添加索引名称便于追踪
                        "detection_time": DataProcessor.extract_base_fields({"@timestamp": None})["Trydate"]
                    }
                    exceptions.append(exception_record)
                    logger.info(f"检测到异常: {field_name}={field_value}, 门限={min_val}~{max_val}")
        
        return exceptions
    
    def process_index_data(self, es_client, index_name: str, query: Dict[str, Any]) -> int:
        """
        处理单个索引的数据
        
        Args:
            es_client: ES客户端
            index_name: 索引名称
            query: 查询条件
            
        Returns:
            处理的文档数量
        """
        processed_count = 0
        exception_count = 0
        
        try:
            logger.info(f"开始处理索引: {index_name}")
            
            # 使用scroll API遍历数据
            for document in es_client.search_with_scroll(index_name, query):
                processed_count += 1
                
                # 检测异常
                exceptions = self.detect_exceptions_in_document(document, index_name)
                if exceptions:
                    self.exception_records.extend(exceptions)
                    exception_count += len(exceptions)
                
                # 每处理1000条记录输出一次进度
                if processed_count % 1000 == 0:
                    logger.info(f"已处理 {processed_count} 条记录，发现 {exception_count} 个异常")
            
            logger.info(f"索引 {index_name} 处理完成: 总记录数={processed_count}, 异常数={exception_count}")
            
        except Exception as e:
            logger.error(f"处理索引 {index_name} 时发生错误: {e}")
            raise
        
        return processed_count
    
    def get_exception_records(self) -> List[Dict[str, Any]]:
        """
        获取所有异常记录
        
        Returns:
            异常记录列表
        """
        return self.exception_records
    
    def clear_exception_records(self):
        """清空异常记录"""
        self.exception_records = []
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取检测统计信息
        
        Returns:
            统计信息字典
        """
        if not self.exception_records:
            return {
                "total_exceptions": 0,
                "exceptions_by_index": {},
                "exceptions_by_field": {}
            }
        
        # 按索引统计
        exceptions_by_index = {}
        exceptions_by_field = {}
        
        for record in self.exception_records:
            index_name = record.get("index_name", "unknown")
            field_name = record.get("key_name", "unknown")
            
            # 按索引统计
            if index_name not in exceptions_by_index:
                exceptions_by_index[index_name] = 0
            exceptions_by_index[index_name] += 1
            
            # 按字段统计
            if field_name not in exceptions_by_field:
                exceptions_by_field[field_name] = 0
            exceptions_by_field[field_name] += 1
        
        return {
            "total_exceptions": len(self.exception_records),
            "exceptions_by_index": exceptions_by_index,
            "exceptions_by_field": exceptions_by_field
        }
